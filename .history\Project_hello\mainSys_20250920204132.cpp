﻿#include <iostream>
#include "enum.h"
using namespace std;

void login(UserType userType) {

}

int main() {
	int select = 0;

	UserTypeClass userTypeClass[] = {
		UserTypeClass(USER_STUDENT, "Student"),
		UserTypeClass(USER_TEACHER, "Teacher"),
		UserTypeClass(USER_MANAGER, "Administrator")
	};

	while (true) {
		system("cls");
		cout << "==========================" << endl;
		for (int i = 0; i < sizeof(userTypeClass) / sizeof(userTypeClass[0]); i++)
		{
			cout << i+1 << "." << userTypeClass[i].label << endl;
		}
		cout << "0.exit" << endl;
		cout << "==========================" << endl;
		cout << "please input��";
		cin >> select;

		bool found = false;
		for (int i = 0; i < sizeof(userTypeClass) / sizeof(userTypeClass[0]); i++) {
			if (select == userTypeClass[i].userType) {
				found = true;
				break;
			}
		}
		if (found) {
			cout << "you select " << userTypeClass[select - 1].label << endl;
			system("pause");
		}
		else if(select == 0){
			exit(0);
		}
		else {
			cout << "input error, please input again!" << endl;
			system("pause");
		}		
	}
	return 0;
}