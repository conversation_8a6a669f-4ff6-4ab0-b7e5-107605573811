﻿#pragma once

#include <string>
using std::string;

// 定义文件路径常量
const string STUDENT_FILE = "student.txt";
const string TEACHER_FILE = "teacher.txt";
const string ADMIN_FILE = "admin.txt";

enum UserType
{
	USER_STUDENT = 1,   // Student
	USER_TEACHER,    // Teacher
	USER_MANAGER  // Manager
};

class UserTypeClass
{
public:
	UserType userType;
	string label;
	string fileName;
	UserTypeClass(UserType type, string str,string file)
		:userType(type), label(str),fileName(file) {};
};
