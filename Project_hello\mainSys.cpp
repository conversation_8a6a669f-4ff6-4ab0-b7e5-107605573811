#include <iostream>
//#include <unordered_map>
#include <map>
#include "enum.h"
using namespace std;

void login(UserType userType) {

}


	// 使用 unordered_map 实现 O(1) 查找效率最高，但遍历排序不确定
	// map就会按顺序
map<int, string> userTypeMap = {
	{USER_STUDENT, "Student"},
	{USER_TEACHER, "Teacher"},
	{USER_MANAGER, "Manager"}
};

int main() {
	int select = 0;

	while (true) {
		system("cls");
		cout << "==========================" << endl;
		// 直接遍历 map 显示选项
		for (const auto& pair : userTypeMap) {
			cout << pair.first << "." << pair.second << endl;
		}
		cout << "0.exit" << endl;
		cout << "==========================" << endl;
		cout << "please input：";
		cin >> select;

		// O(1) 时间复杂度查找
		auto it = userTypeMap.find(select);
		if (it != userTypeMap.end()) {
			cout << "you select " << it->second << endl;
			system("pause");
		}
		else if (select == 0) {
			exit(0);
		}
		else {
			cout << "input error, please input again!" << endl;
			system("pause");
		}
	}
	return 0;
}